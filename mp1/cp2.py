from user import User
from utils import distance_km


def cp2_1_simple_inference(dictUsers):
    dictUsersInferred = dict()  # dict to return, store inferred results here
    # you should keep everything in dictUsers as is / read-only

    for user_id, user in dictUsers.items():
        inferred_user = User(user)

        if user.latlon_valid() and user.home_shared:
            pass
        else:
            friends_with_shared_homes = []
            for friend_id in user.friends:
                if friend_id in dictUsers:
                    friend = dictUsers[friend_id]
                    if friend.latlon_valid() and friend.home_shared:
                        friends_with_shared_homes.append(friend)

            if friends_with_shared_homes:
                total_lat = sum(friend.home_lat for friend in friends_with_shared_homes)
                total_lon = sum(friend.home_lon for friend in friends_with_shared_homes)
                num_friends = len(friends_with_shared_homes)

                inferred_user.home_lat = total_lat / num_friends
                inferred_user.home_lon = total_lon / num_friends

        dictUsersInferred[user_id] = inferred_user

    return dictUsersInferred


def cp2_2_improved_inference(dictUsers):
    dictUsersInferred = dict()

    for user_id, user in dictUsers.items():
        dictUsersInferred[user_id] = User(user)

    # Two-pass algorithm: first pass like simple, second pass uses inferred results
    for iteration in range(2):
        improved_count = 0

        for user_id, user in dictUsers.items():
            inferred_user = dictUsersInferred[user_id]

            if user.latlon_valid() and user.home_shared:
                continue

            if iteration > 0 and inferred_user.latlon_valid() and not user.home_shared:
                continue

            friends_with_locations = []

            for friend_id in user.friends:
                if friend_id in dictUsersInferred:
                    friend_inferred = dictUsersInferred[friend_id]
                    friend_original = dictUsers[friend_id]

                    if friend_inferred.latlon_valid() and friend_original.home_shared:
                        friends_with_locations.append(friend_inferred)

            # In second iteration, also use previously inferred friends (but with caution)
            if iteration > 0:
                for friend_id in user.friends:
                    if friend_id in dictUsersInferred:
                        friend_inferred = dictUsersInferred[friend_id]
                        friend_original = dictUsers[friend_id]

                        if (
                            friend_inferred.latlon_valid()
                            and not friend_original.home_shared
                            and friend_inferred not in friends_with_locations
                        ):
                            friends_with_locations.append(friend_inferred)

            # Add friends-of-friends only if we have very few direct friends
            if len(friends_with_locations) == 0:
                for friend_id in user.friends:
                    if friend_id in dictUsers:
                        friend = dictUsers[friend_id]
                        for fof_id in friend.friends:
                            if fof_id != user_id and fof_id in dictUsersInferred:
                                fof_inferred = dictUsersInferred[fof_id]
                                fof_original = dictUsers[fof_id]
                                if (
                                    fof_inferred.latlon_valid()
                                    and fof_original.home_shared
                                    and fof_inferred not in friends_with_locations
                                ):
                                    friends_with_locations.append(fof_inferred)
                                    if (
                                        len(friends_with_locations) >= 3
                                    ):  # Limit FoF to avoid noise
                                        break
                        if len(friends_with_locations) >= 3:
                            break

            # Improved centroid calculation
            if friends_with_locations:
                if len(friends_with_locations) <= 2:
                    total_lat = sum(f.home_lat for f in friends_with_locations)
                    total_lon = sum(f.home_lon for f in friends_with_locations)
                    inferred_user.home_lat = total_lat / len(friends_with_locations)
                    inferred_user.home_lon = total_lon / len(friends_with_locations)
                else:
                    # For larger groups, remove potential outliers
                    # Calculate distances from each point to all others
                    friend_scores = []
                    for i, friend in enumerate(friends_with_locations):
                        total_distance = 0
                        for j, other_friend in enumerate(friends_with_locations):
                            if i != j:
                                dist = distance_km(
                                    friend.home_lat,
                                    friend.home_lon,
                                    other_friend.home_lat,
                                    other_friend.home_lon,
                                )
                                total_distance += dist
                        avg_distance = total_distance / (
                            len(friends_with_locations) - 1
                        )
                        friend_scores.append((friend, avg_distance))

                    # Sort by average distance and keep the most central ones
                    friend_scores.sort(key=lambda x: x[1])
                    num_to_keep = max(
                        2, int(len(friends_with_locations) * 0.8)
                    )  # Keep 80%
                    central_friends = [fs[0] for fs in friend_scores[:num_to_keep]]

                    total_lat = sum(f.home_lat for f in central_friends)
                    total_lon = sum(f.home_lon for f in central_friends)
                    inferred_user.home_lat = total_lat / len(central_friends)
                    inferred_user.home_lon = total_lon / len(central_friends)

                improved_count += 1

        # If no improvements in this iteration, stop early
        if improved_count == 0:
            break

    return dictUsersInferred


def cp2_calc_accuracy(truth_dict, inferred_dict):
    # distance_km(a,b): return distance between a and be in km
    # recommended standard: is accuate if distance to ground truth < 25km
    if len(truth_dict) != len(inferred_dict) or len(truth_dict) == 0:
        return 0.0
    sum = 0
    for i in truth_dict:
        if truth_dict[i].home_shared:
            sum += 1
        elif truth_dict[i].latlon_valid() and inferred_dict[i].latlon_valid():
            if (
                distance_km(
                    truth_dict[i].home_lat,
                    truth_dict[i].home_lon,
                    inferred_dict[i].home_lat,
                    inferred_dict[i].home_lon,
                )
                < 25.0
            ):
                sum += 1
    return sum * 1.0 / len(truth_dict)
