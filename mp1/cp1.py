import csv
from logging import warn, error, debug
from user import User


## parse homes.txt
#  input:
#    f: filename
#  output:
#    a dict of all users from homes.txt with key=user_id, value=User object
def cp1_1_parse_homes(f):
    dictUsers_out = dict()
    with open(f) as csv_f:
        for i in csv.reader(csv_f):
            user_id = int(i[0].strip())

            if len(i) == 4:
                home_lat = float(i[1].strip())
                home_lon = float(i[2].strip())
                home_shared = bool(int(i[3].strip()))
            else:
                home_lat = -999
                home_lon = -999
                home_shared = False

            user = User(user_id, home_lat, home_lon, home_shared)
            dictUsers_out[user_id] = user

    return dictUsers_out


## parse friends.txt
#  input:
#    f: filename
#    dictUsers: dictionary of users, output of cp1_1_parse_homes()
#  no output, modify dictUsers directly
def cp1_2_parse_friends(f, dictUsers):
    with open(f) as csv_f:
        for i in csv.reader(csv_f):
            user_id1 = int(i[0].strip())
            user_id2 = int(i[1].strip())

            if user_id1 not in dictUsers:
                dictUsers[user_id1] = User(user_id1)
            if user_id2 not in dictUsers:
                dictUsers[user_id2] = User(user_id2)

            dictUsers[user_id1].friends.add(user_id2)
            dictUsers[user_id2].friends.add(user_id1)


# return all answers to Checkpoint 1.3 of MP Handout in variables
# order is given in the template
def cp1_3_answers(dictUsers):
    u_cnt = len(dictUsers)

    # 2. Count users with unknown locations in dataset 1
    # In dataset 1, "unknown locations" means home_shared = False
    # (even though the true location is available for evaluation)
    u_noloc_cnt = 0
    for user in dictUsers.values():
        if user.latlon_valid() and not user.home_shared:
            u_noloc_cnt += 1

    # 3. Count users with unknown locations AND no friends in dataset 1
    u_noloc_nofnds_cnt = 0
    for user in dictUsers.values():
        if user.latlon_valid() and not user.home_shared and len(user.friends) == 0:
            u_noloc_nofnds_cnt += 1

    # 4. Baseline accuracy (p_b): accuracy of incorrectly predicting all unknown locations
    # This means we get 0 correct inferences for unknown locations
    # Accuracy = (shared locations + 0 correct inferences) / total users
    shared_locations = u_cnt - u_noloc_cnt
    p_b = shared_locations / u_cnt

    # 5. Upper bound accuracy (p_u1): can infer correctly UNLESS user has NO friends
    # Users we can infer correctly = users with unknown locations who HAVE friends
    users_with_unknown_and_friends = u_noloc_cnt - u_noloc_nofnds_cnt
    p_u1 = (shared_locations + users_with_unknown_and_friends) / u_cnt

    # 6. Upper bound accuracy (p_u2): can infer correctly UNLESS user has NO friends who shared locations
    # Count users with unknown locations who have NO friends with shared locations
    u_noloc_no_shared_friends = 0
    for user in dictUsers.values():
        if user.latlon_valid() and not user.home_shared:
            # Check if this user has any friends who shared their locations
            has_shared_friend = False
            for friend_id in user.friends:
                if friend_id in dictUsers:
                    friend = dictUsers[friend_id]
                    if friend.latlon_valid() and friend.home_shared:
                        has_shared_friend = True
                        break
            if not has_shared_friend:
                u_noloc_no_shared_friends += 1

    users_with_unknown_and_shared_friends = u_noloc_cnt - u_noloc_no_shared_friends
    p_u2 = (shared_locations + users_with_unknown_and_shared_friends) / u_cnt

    return u_cnt, u_noloc_cnt, u_noloc_nofnds_cnt, p_b, p_u1, p_u2
